# 4-Hour Trading Strategy Mapping

## Academic Research to Freqtrade Implementation

This document maps the academic trading research summaries to concrete 4-hour trading strategies suitable for Freqtrade implementation.

## Selected Strategies for 4-Hour Timeframe

### 1. Smart Money Volume Breakout Strategy

**Source:** [Smart Money Trading](summaries/advanced-strategies/smart-money-trading.md) + [Volume Spike Trading](summaries/advanced-strategies/volume-spike-trading.md)

**Core Hypothesis:** When institutional money enters positions, it creates detectable volume and price patterns on 4h charts.

**Implementation Logic:**
```python
# Volume spike detection
dataframe['volume_avg'] = ta.SMA(dataframe['volume'], timeperiod=20)
dataframe['volume_spike'] = dataframe['volume'] > (dataframe['volume_avg'] * 2.0)

# VWAP analysis
dataframe['vwap'] = qtpylib.vwap(dataframe)
dataframe['close_above_vwap'] = dataframe['close'] > dataframe['vwap']

# Market structure
dataframe['swing_high'] = dataframe['high'].rolling(window=5).max()
dataframe['structure_break'] = dataframe['close'] > dataframe['swing_high'].shift(1)

# Entry signal
dataframe['smart_money_entry'] = (
    dataframe['volume_spike'] &
    dataframe['close_above_vwap'] &
    dataframe['structure_break']
)
```

**Entry Rules:**
- Volume spike (>2x average) on 4h candle
- Close above VWAP
- Break of previous swing high
- Enter above high of signal candle

**Exit Rules:**
- Stop loss below signal candle low
- Take profit at next resistance level
- Trail stop as price advances

### 2. VWAP Pullback Continuation Strategy

**Source:** [VWAP Trading](summaries/day-trading/vwap-trading.md)

**Core Hypothesis:** In trending markets, pullbacks to VWAP provide optimal entry points with favorable risk/reward.

**Implementation Logic:**
```python
# VWAP calculation
dataframe['vwap'] = qtpylib.vwap(dataframe)

# Trend determination
dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=12)
dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=26)
dataframe['uptrend'] = dataframe['ema_fast'] > dataframe['ema_slow']

# VWAP pullback
dataframe['near_vwap'] = abs(dataframe['close'] - dataframe['vwap']) / dataframe['vwap'] < 0.005
dataframe['pullback_entry'] = (
    dataframe['uptrend'] &
    dataframe['near_vwap'] &
    (dataframe['close'] > dataframe['vwap'])
)
```

### 3. Supply/Demand Zone Reversal Strategy

**Source:** [Supply and Demand Zones](summaries/advanced-strategies/supply-demand-zones.md)

**Core Hypothesis:** Fresh supply/demand zones created by aggressive moves provide high-probability reversal points.

**Implementation Logic:**
```python
# ATR for aggressive move detection
dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
dataframe['aggressive_move'] = (dataframe['high'] - dataframe['low']) > (dataframe['atr'] * 1.5)

# Zone identification
dataframe['supply_zone'] = dataframe['high'].rolling(window=5).max()
dataframe['demand_zone'] = dataframe['low'].rolling(window=5).min()

# Rejection signals
dataframe['pin_bar'] = (
    (dataframe['high'] - dataframe['close']) > (dataframe['close'] - dataframe['open']) * 2
)
```

### 4. Market Structure Break and Retest Strategy

**Source:** [Market Structure](summaries/foundations/market-structure.md) + [Smart Money Trading](summaries/advanced-strategies/smart-money-trading.md)

**Core Hypothesis:** Structure breaks followed by retests provide high-probability entries in breakout direction.

**Implementation Logic:**
```python
# Swing point identification
dataframe['swing_high'] = dataframe['high'].rolling(window=5, center=True).max() == dataframe['high']
dataframe['swing_low'] = dataframe['low'].rolling(window=5, center=True).min() == dataframe['low']

# Structure break detection
dataframe['bullish_break'] = dataframe['close'] > dataframe['swing_high'].shift(1)
dataframe['bearish_break'] = dataframe['close'] < dataframe['swing_low'].shift(1)

# Retest identification
dataframe['retest_level'] = dataframe['swing_high'].shift(1).fillna(method='ffill')
dataframe['retest_entry'] = (
    dataframe['bullish_break'].shift(1) &
    (abs(dataframe['low'] - dataframe['retest_level']) / dataframe['retest_level'] < 0.01)
)
```

### 5. Multi-Timeframe Confluence Strategy

**Source:** [Multiple Timeframe Analysis](summaries/technical-tools/multiple-timeframe-analysis.md)

**Core Hypothesis:** Alignment of signals across multiple timeframes increases success probability.

**Implementation Logic:**
```python
# Higher timeframe trend (using informative pairs)
def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
    # Get daily data for trend
    inf_tf = '1d'
    informative = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=inf_tf)
    informative['ema_trend'] = ta.EMA(informative, timeperiod=20)
    informative['daily_trend'] = informative['close'] > informative['ema_trend']

    # Merge with 4h data
    dataframe = merge_informative_pair(dataframe, informative, self.timeframe, inf_tf, ffill=True)

    return dataframe
```

## Risk Management Framework

### Position Sizing
- **Base position size:** 2-3% of account per trade
- **Reduce size** when multiple strategies signal simultaneously
- **Increase size** when confluence is high

### Stop Loss Rules
- **Smart Money Strategy:** Below signal candle low
- **VWAP Strategy:** Below VWAP level
- **Supply/Demand:** Beyond zone boundary
- **Structure Break:** Below retest level

### Take Profit Targets
- **Primary target:** Next major resistance/support level
- **Secondary target:** 2:1 or 3:1 risk/reward ratio
- **Trail stops** in trending moves

## Implementation Priority

1. **Phase 1:** Implement Smart Money Volume Breakout (most robust)
2. **Phase 2:** Add VWAP Pullback Strategy (good risk/reward)
3. **Phase 3:** Integrate Structure Break/Retest (high win rate)
4. **Phase 4:** Test Supply/Demand Zones (advanced)
5. **Phase 5:** Develop Multi-Timeframe Confluence (most complex)

## Backtesting Parameters

### Recommended Settings
- **Timeframe:** 4h
- **Pairs:** BTC/USDT, ETH/USDT (high liquidity)
- **Test Period:** 2023-01-01 to 2024-12-31
- **Initial Capital:** $10,000
- **Max Open Trades:** 3
- **Stake Amount:** 3% per trade

### Key Metrics to Monitor
- **Win Rate:** Target >60%
- **Profit Factor:** Target >1.5
- **Max Drawdown:** Keep <15%
- **Sharpe Ratio:** Target >1.0
- **Average Trade Duration:** 12-48 hours (3-12 candles)

## Next Steps

1. Implement core strategy in `AdvancedStrategy_4h.py`
2. Configure backtesting environment
3. Run initial backtests on selected pairs
4. Optimize parameters using hyperopt
5. Add risk management overlay
6. Test with paper trading before live deployment

---

*This mapping provides the foundation for Task 4: Implement Core 4h Trading Strategy*