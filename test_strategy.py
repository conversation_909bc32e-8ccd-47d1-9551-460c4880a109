#!/usr/bin/env python3
"""
Simple strategy test script to verify AdvancedStrategy_4h works correctly
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append('.')

# Mock TA-Lib functions for testing
class MockTA:
    @staticmethod
    def EMA(dataframe, timeperiod=14):
        return dataframe['close'].ewm(span=timeperiod).mean()

    @staticmethod
    def SMA(data, timeperiod=14):
        return data.rolling(window=timeperiod).mean()

    @staticmethod
    def ATR(dataframe, timeperiod=14):
        high_low = dataframe['high'] - dataframe['low']
        high_close = np.abs(dataframe['high'] - dataframe['close'].shift())
        low_close = np.abs(dataframe['low'] - dataframe['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(window=timeperiod).mean()

# Mock qtpylib
class MockQtpylib:
    @staticmethod
    def vwap(dataframe):
        # Simple VWAP calculation
        typical_price = (dataframe['high'] + dataframe['low'] + dataframe['close']) / 3
        return (typical_price * dataframe['volume']).cumsum() / dataframe['volume'].cumsum()

# Patch imports
sys.modules['talib.abstract'] = MockTA
sys.modules['freqtrade.vendor.qtpylib.indicators'] = MockQtpylib

try:
    # Import our strategy
    from user_data.strategies.AdvancedStrategy_4h import AdvancedStrategy_4h
    print("✅ Strategy import successful!")

    # Create strategy instance
    strategy = AdvancedStrategy_4h()
    print(f"✅ Strategy instance created: {strategy.__class__.__name__}")
    print(f"   Timeframe: {strategy.timeframe}")
    print(f"   Stop Loss: {strategy.stoploss}")
    print(f"   ROI: {strategy.minimal_roi}")

    # Create sample data for testing
    print("\n📊 Creating sample data...")
    dates = pd.date_range(start='2024-01-01', end='2024-01-10', freq='4H')
    n_candles = len(dates)

    # Generate realistic OHLCV data
    np.random.seed(42)  # For reproducible results

    # Starting price
    base_price = 50000

    # Generate price data with some trend and volatility
    price_changes = np.random.normal(0, 0.02, n_candles)  # 2% volatility
    prices = [base_price]

    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    # Create OHLC from prices
    opens = prices[:-1]
    closes = prices[1:]

    # Generate highs and lows
    highs = []
    lows = []
    volumes = []

    for i in range(len(opens)):
        open_price = opens[i]
        close_price = closes[i]

        # High is max of open/close + some random amount
        high = max(open_price, close_price) * (1 + np.random.uniform(0, 0.01))
        # Low is min of open/close - some random amount
        low = min(open_price, close_price) * (1 - np.random.uniform(0, 0.01))

        # Volume between 100-1000
        volume = np.random.uniform(100, 1000)

        highs.append(high)
        lows.append(low)
        volumes.append(volume)

    # Create DataFrame
    dataframe = pd.DataFrame({
        'date': dates[1:],  # Skip first date since we use it for opens
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })

    print(f"   Created {len(dataframe)} candles")
    print(f"   Date range: {dataframe['date'].min()} to {dataframe['date'].max()}")
    print(f"   Price range: ${dataframe['low'].min():.2f} - ${dataframe['high'].max():.2f}")

    # Test populate_indicators
    print("\n🔧 Testing populate_indicators...")
    metadata = {'pair': 'BTC/USDT'}

    try:
        result_df = strategy.populate_indicators(dataframe.copy(), metadata)
        print("✅ populate_indicators successful!")

        # Check if key indicators were added
        expected_indicators = [
            'ema_fast', 'ema_slow', 'vwap', 'atr', 'volume_avg',
            'smart_money_signal', 'order_block_signal', 'vwap_signal'
        ]

        missing_indicators = []
        for indicator in expected_indicators:
            if indicator not in result_df.columns:
                missing_indicators.append(indicator)

        if missing_indicators:
            print(f"⚠️  Missing indicators: {missing_indicators}")
        else:
            print(f"✅ All expected indicators present: {len(expected_indicators)} indicators")

        print(f"   Total columns: {len(result_df.columns)}")

    except Exception as e:
        print(f"❌ populate_indicators failed: {e}")
        sys.exit(1)

    # Test populate_entry_trend
    print("\n📈 Testing populate_entry_trend...")
    try:
        entry_df = strategy.populate_entry_trend(result_df.copy(), metadata)
        print("✅ populate_entry_trend successful!")

        if 'enter_long' in entry_df.columns:
            entry_signals = entry_df['enter_long'].sum()
            print(f"   Entry signals generated: {entry_signals}")
        else:
            print("⚠️  No 'enter_long' column found")

    except Exception as e:
        print(f"❌ populate_entry_trend failed: {e}")
        sys.exit(1)

    # Test populate_exit_trend
    print("\n📉 Testing populate_exit_trend...")
    try:
        exit_df = strategy.populate_exit_trend(entry_df.copy(), metadata)
        print("✅ populate_exit_trend successful!")

        if 'exit_long' in exit_df.columns:
            exit_signals = exit_df['exit_long'].sum()
            print(f"   Exit signals generated: {exit_signals}")
        else:
            print("⚠️  No 'exit_long' column found")

    except Exception as e:
        print(f"❌ populate_exit_trend failed: {e}")
        sys.exit(1)

    print("\n🎉 All tests passed! Strategy is working correctly.")
    print("\n📋 Strategy Summary:")
    print(f"   Name: {strategy.__class__.__name__}")
    print(f"   Timeframe: {strategy.timeframe}")
    print(f"   Interface Version: {strategy.interface_version}")
    print(f"   Stop Loss: {strategy.stoploss * 100}%")
    print(f"   Trailing Stop: {strategy.trailing_stop}")
    print(f"   Use Exit Signal: {strategy.use_exit_signal}")

    # Show sample of final dataframe
    print(f"\n📊 Sample of processed data (last 5 rows):")
    display_cols = ['date', 'close', 'volume', 'ema_fast', 'ema_slow', 'vwap', 'enter_long', 'exit_long']
    available_cols = [col for col in display_cols if col in exit_df.columns]
    print(exit_df[available_cols].tail().to_string(index=False))

except ImportError as e:
    print(f"❌ Failed to import strategy: {e}")
    print("Make sure the strategy file exists and has no syntax errors.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
